<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Malva Trading LTD - Trading, Construction, Manufacturing</title>
    <meta name="description" content="Malva Trading Ltd is a dynamic company specializing in trading, construction materials, and manufacturing services in Rwanda.">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<body>

    <header>
        <div class="header-container">
            <a href="#main" class="brand" data-aos="fade-down" data-aos-duration="800">Malva Trading LTD</a>
            <nav class="navigation">
                <a href="#main" data-aos="fade-down" data-aos-duration="800" data-aos-delay="100">Home</a>
                <a href="#about"data-aos="fade-down" data-aos-duration="800" data-aos-delay="200">About</a>
                <a href="#services" data-aos="fade-down" data-aos-duration="800" data-aos-delay="300">Services</a>
                <a href="#business" data-aos="fade-down" data-aos-duration="800" data-aos-delay="400">Business</a>
                <a href="#gallery" data-aos="fade-down" data-aos-duration="800" data-aos-delay="500">Gallery</a>
                <a href="#contact" data-aos="fade-down" data-aos-duration="800" data-aos-delay="600">Contact</a>
            </nav>
            <div class="menu-btn"><span></span></div>
        </div>
    </header>

    <section class="main" id="main">
        <div class="section-container">
            <div class="hero-container">
                <div class="main-content">
                    <h1 data-aos="fade-right" data-aos-duration="1000">Driving Innovation in Rwanda</h1>
                    <p class="subtitle" data-aos="fade-right" data-aos-duration="1000" data-aos-delay="200">Your trusted partner in Trading, Construction, and Manufacturing solutions.</p>

                    <a href="#contact" class="btn hero-btn" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="400">Get In Touch</a>

                    <div class="media-icons">
                        <a href="https://wa.me/250788307386" target="_blank" aria-label="Contact Malva Trading on WhatsApp" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="600"><i class="fab fa-whatsapp"></i></a>
                        <a href="https://x.com/ngogapaul51" target="_blank" aria-label="Malva Trading on X" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="700"><i class="fab fa-x-twitter"></i></a>
                        <a href="https://www.instagram.com/malvatrading?igsh=MXNjNnV6eDh1eXVxYg==" target="_blank" aria-label="Malva Trading on Instagram" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="800"><i class="fab fa-instagram"></i></a>
                        <a href="https://www.facebook.com/share/1AT84mVnRx/" target="_blank" aria-label="Malva Trading on Facebook" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="900"><i class="fab fa-facebook"></i></a>
                        <a href="https://www.tiktok.com/@malva.trading.ltd?_t=ZM-8wIBIURlruf&_r=1" target="_blank" aria-label="Malva Trading on TikTok" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="1000"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
                <div class="hero-image" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="300">
                    <img src="https://images.unsplash.com/photo-1621905251189-08b45d6a269e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTR8fGNvbnN0cnVjdGlvbiUyMGVxdWlwbWVudHxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=80" alt="Construction machinery">
                </div>
            </div>
        </div>
    </section>

    <section class="about" id="about">
        <div class="section-container">
            <div class="title" data-aos="fade-up" data-aos-duration="800">
                <h2 class="section-title">About Us</h2>
            </div>
            <div class="content two-column-layout">
                <div class="column col-left" data-aos="fade-right" data-aos-duration="1000">
                    <div class="img-card">
                        <img src="Image/image4.jpg" alt="Team meeting or relevant 'about us' visual">
                    </div>
                </div>
                <div class="column col-right" data-aos="fade-left" data-aos-duration="1000">
                    <h3 class="content-title">Welcome to Malva Trading LTD</h3>
                    <p class="paragraph-text">
                        Malva Trading Ltd is a dynamic and rapidly growing company dedicated to driving innovation in the trading, construction, and manufacturing sectors. Founded with a commitment to excellence, we specialize in providing high-quality products and services that contribute to the economic growth and infrastructure development of Rwanda.
                    </p>
                    <p class="paragraph-text">
                        Our mission is to deliver superior solutions, create employment opportunities, and establish long-term partnerships with clients and stakeholders. At Malva Trading Ltd, we believe in integrity, professionalism, and customer satisfaction. With years of experience in marketing, sales, and business development, we have successfully positioned ourselves as a trusted name in the industry.
                    </p>
                    <a href="#contact" class="btn" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">Learn More</a>
                </div>
            </div>
        </div>
    </section>

    <section class="services" id="services">
       <div class="section-container">
            <div class="title" data-aos="fade-up" data-aos-duration="800">
                <h2 class="section-title">Our Services</h2>
                <p class="section-subtitle">
                    We provide a wide range of services to support client needs across diverse industries, ensuring quality products and professional delivery that meet international standards.
                </p>
            </div>
            <div class="content card-grid-3">
                <div class="card" data-aos="fade-up" data-aos-duration="800" data-aos-delay="100">
                    <div class="service-icon">
                        <i class="fas fa-dolly"></i> </div>
                    <div class="info">
                        <h3>Trading & Supply</h3>
                        <p>Specializing in importing, exporting, and distributing high-quality products including:</p>
                        <ul> <li>Construction materials (stainless steel balustrades, glass shower cabins)</li>
                            <li>Supply & fabrication (aluminium doors/windows, gypsum ceilings/partitions)</li>
                            <li>MDF kitchens, wardrobes, etc.</li>
                            <li>Industrial and sanitary household goods</li>
                        </ul>
                    </div>
                </div>
                <div class="card" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
                    <div class="service-icon">
                         <i class="fas fa-hard-hat"></i> </div>
                    <div class="info">
                        <h3>Construction Support</h3>
                        <p>We work closely with contractors and developers, supplying essential materials and logistical support for construction projects of all scales.</p>
                    </div>
                </div>
                <div class="card" data-aos="fade-up" data-aos-duration="800" data-aos-delay="300">
                    <div class="service-icon">
                        <i class="fas fa-industry"></i> </div>
                    <div class="info">
                        <h3>Manufacturing</h3>
                        <p>We produce high-quality cleaning products in our local manufacturing facility, reducing import dependency and supporting Rwanda's industrial growth.</p>
                        <ul>
                            <li>Multi-purpose liquid soap for general cleaning</li>
                            <li>Specialized liquid detergent for toilet cleaning</li>
                        </ul>
                   </div>
                </div>
            </div>
       </div>
    </section>

    <section class="business dark-section" id="business">
         <div class="section-container">
            <div class="title" data-aos="fade-up" data-aos-duration="800">
                <h2 class="section-title">Our Business Approach</h2>
            </div>
            <div class="content two-column-layout reverse-layout-mobile"> <div class="column col-left" data-aos="fade-right" data-aos-duration="1000">
                     <div class="img-card">
                         <img src="Image/malva3.jpg" alt="Visual representing business operations or market">
                     </div>
                 </div>
                 <div class="column col-right" data-aos="fade-left" data-aos-duration="1000">
                    <h3 class="content-title">Multi-Sector Expertise</h3>
                    <p class="paragraph-text">
                         Malva Trading Ltd operates across multiple sectors, offering a diverse range of products and services tailored to meet the needs of individuals, businesses, and government projects in Rwanda and the region.
                    </p>
                    <p class="paragraph-text">
                         Our structure enables efficient handling of large-scale supply chain management, distribution, and construction material trading. With deep market understanding, we aim to bridge the gap between quality suppliers and growing industries, ensuring reliability and value.
                     </p>
                     <p class="paragraph-text">
                         Our manufacturing division produces high-quality cleaning products including multi-purpose liquid soap and specialized toilet detergents. We focus on environmentally responsible formulations that meet the highest standards of effectiveness and safety for both residential and commercial applications.
                     </p>
                 </div>
             </div>
         </div>
    </section>

    <section class="gallery" id="gallery">
        <div class="section-container">
             <div class="title" data-aos="fade-up" data-aos-duration="800">
                 <h2 class="section-title">Our Gallery</h2>
                 <p class="section-subtitle">Showcasing our premium construction materials and products.</p>
             </div>

             <!-- Gallery filter buttons -->
             <div class="gallery-filter" data-aos="fade-up" data-aos-duration="800">
                 <button class="filter-btn active" data-filter="all">All</button>
                 <button class="filter-btn" data-filter="construction">Construction</button>
                 <button class="filter-btn" data-filter="glass">Glass & Railings</button>
                 <button class="filter-btn" data-filter="steel">Aluminium Products</button>
                 <button class="filter-btn" data-filter="manufacturing">Cleaning Products</button>
             </div>

             <div class="gallery-container">
                 <!-- Aluminum Products (Moved from Glass & Railings) -->
                 <div class="gallery-item steel construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="100">
                     <div class="gallery-img">
                         <img src="Image/imagee1.jpg" alt="Aluminum Product">
                     </div>
                 </div>
                 <div class="gallery-item steel construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="150">
                     <div class="gallery-img">
                         <img src="Image/imagee2.jpg" alt="Aluminum Product">
                     </div>
                 </div>
                 <div class="gallery-item steel construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
                     <div class="gallery-img">
                         <img src="Image/imagee3.jpg" alt="Aluminum Product">
                     </div>
                 </div>
                 <div class="gallery-item steel construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="250">
                     <div class="gallery-img">
                         <img src="Image/imagee4.jpg" alt="Aluminum Product">
                     </div>
                 </div>
                 <div class="gallery-item steel construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="300">
                     <div class="gallery-img">
                         <img src="Image/imagee5.jpg" alt="Aluminum Product">
                     </div>
                 </div>
                 <div class="gallery-item construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="250">
                     <div class="gallery-img">
                         <img src="Image/image9.jpg" alt="Construction Project">
                     </div>
                 </div>

                 <div class="gallery-item construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="350">
                     <div class="gallery-img">
                         <img src="Image/image11.jpg" alt="Construction Site">
                     </div>
                 </div>

                 <!-- Steel Products -->
                 <div class="gallery-item steel construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="350">
                     <div class="gallery-img">
                         <img src="img/stainless_steel_tube.jpg" alt="Stainless Steel Tube">
                     </div>
                 </div>
                 <!-- Glass Railing Products -->
                 <div class="gallery-item glass construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="400">
                     <div class="gallery-img">
                         <img src="img/egoee_stainless_steel.jpg" alt="Glass Railing Product">
                     </div>
                 </div>

                 <div class="gallery-item glass construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="500">
                     <div class="gallery-img">
                         <img src="img/handrail_post.jpg" alt="Stainless Steel Handrail Post">
                     </div>
                 </div>
                 <div class="gallery-item glass construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="550">
                     <div class="gallery-img">
                         <img src="img/end_cap.jpg" alt="Stainless Steel End Cap">
                     </div>
                 </div>

                 <!-- Glass Shower Cabins -->
                 <div class="gallery-item glass manufacturing construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="600">
                     <div class="gallery-img">
                         <img src="img/glass_shower_cabin.jpg" alt="Glass Shower Cabin">
                     </div>
                 </div>
                 <div class="gallery-item glass manufacturing construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="650">
                     <div class="gallery-img">
                         <img src="img/glass_shower_cabin_1.jpg" alt="Modern Glass Shower Cabin">
                     </div>
                 </div>
                 <div class="gallery-item glass manufacturing construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="700">
                     <div class="gallery-img">
                         <img src="img/glass_shower_cabin_2.jpg" alt="Luxury Glass Shower Cabin">
                     </div>
                 </div>
                 <div class="gallery-item glass manufacturing construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="750">
                     <div class="gallery-img">
                         <img src="img/glass_shower_cabin_3.jpg" alt="Premium Glass Shower Cabin">
                     </div>
                 </div>
                 <div class="gallery-item glass manufacturing construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="800">
                     <div class="gallery-img">
                         <img src="img/glass_shower_cabin_4.jpg" alt="Custom Glass Shower Cabin">
                     </div>
                 </div>
                 <div class="gallery-item glass manufacturing construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="850">
                     <div class="gallery-img">
                         <img src="Image/image12.jpg" alt="Staircase with Glass Railing">
                     </div>
                 </div>

                 <!-- Glass Pergola Products -->
                 <div class="gallery-item glass construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="900">
                     <div class="gallery-img">
                         <img src="glass pergola/glass-pergola-1.jpeg" alt="Glass Pergola Design 1">
                     </div>
                 </div>
                 <div class="gallery-item glass construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="950">
                     <div class="gallery-img">
                         <img src="glass pergola/glass-pergola-2.jpeg" alt="Glass Pergola Design 2">
                     </div>
                 </div>
                 <div class="gallery-item glass construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="1000">
                     <div class="gallery-img">
                         <img src="glass pergola/glass-pergola-3.jpeg" alt="Glass Pergola Design 3">
                     </div>
                 </div>
                 <div class="gallery-item glass construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="1050">
                     <div class="gallery-img">
                         <img src="glass pergola/glass-pergola-4.jpeg" alt="Glass Pergola Design 4">
                     </div>
                 </div>
                 <div class="gallery-item glass construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="1100">
                     <div class="gallery-img">
                         <img src="glass pergola/glass-pergola-5.jpeg" alt="Glass Pergola Design 5">
                     </div>
                 </div>

                 <!-- Glass Balcony Products -->
                 <div class="gallery-item glass construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="1150">
                     <div class="gallery-img">
                         <img src="glass balcony/glass-balcony-1.jpeg" alt="Glass Balcony Design 1">
                     </div>
                 </div>
                 <div class="gallery-item glass construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="1200">
                     <div class="gallery-img">
                         <img src="glass balcony/glass-balcony-2.jpeg" alt="Glass Balcony Design 2">
                     </div>
                 </div>

                 <!-- Staircase Products -->
                 <div class="gallery-item steel construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="1250">
                     <div class="gallery-img">
                         <img src="staircase/staircase-1.jpeg" alt="Staircase Design 1">
                     </div>
                 </div>
                 <div class="gallery-item steel construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="1300">
                     <div class="gallery-img">
                         <img src="staircase/staircase-2.jpeg" alt="Staircase Design 2">
                     </div>
                 </div>
                 <div class="gallery-item steel construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="1350">
                     <div class="gallery-img">
                         <img src="staircase/staircase-3.jpeg" alt="Staircase Design 3">
                     </div>
                 </div>
                 <div class="gallery-item steel construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="1400">
                     <div class="gallery-img">
                         <img src="staircase/staircase-4.jpeg" alt="Staircase Design 4">
                     </div>
                 </div>
                 <div class="gallery-item steel construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="1450">
                     <div class="gallery-img">
                         <img src="staircase/staircase-5.jpeg" alt="Staircase Design 5">
                     </div>
                 </div>
                 <div class="gallery-item steel construction" data-aos="fade-up" data-aos-duration="800" data-aos-delay="1500">
                     <div class="gallery-img">
                         <img src="staircase/staircase-6.jpeg" alt="Staircase Design 6">
                     </div>
                 </div>

                 <!-- Manufacturing Products -->
                 <div class="gallery-item manufacturing" data-aos="fade-up" data-aos-duration="800" data-aos-delay="400">
                     <div class="gallery-img">
                         <img src="Image/image7.png" alt="Multi-purpose Liquid Soap">
                     </div>
                 </div>
                 <div class="gallery-item manufacturing" data-aos="fade-up" data-aos-duration="800" data-aos-delay="450">
                     <div class="gallery-img">
                         <img src="Image/image8.jpg" alt="Toilet Detergents">
                     </div>
                 </div>
             </div>
        </div>
    </section>

    <section class="contact dark-section" id="contact">
        <div class="section-container">
            <div class="title" data-aos="fade-up" data-aos-duration="800">
              <h2 class="section-title">Contact Us</h2>
              <p class="section-subtitle">We'd love to hear from you. Reach out with questions or project inquiries.</p>
            </div>
            <div class="content contact-content">
              <div class="contact-info" data-aos="fade-right" data-aos-duration="1000">
                 <h3 class="content-title">Get in Touch</h3>
                 <p>Use the details below or fill out the contact form.</p>
                 <div class="info-card">
                   <div class="contact-icon"><i class="fas fa-map-marker-alt"></i></div>
                   <div class="info">
                     <h4>Address</h4>
                     <span><a href="https://maps.google.com/?q=-1.923928,30.07105" target="_blank" style="color: inherit; text-decoration: none;">KG 665 St, Kigali, Rwanda</a></span> </div>
                 </div>
                 <div class="info-card">
                   <div class="contact-icon"><i class="fas fa-phone"></i></div>
                   <div class="info">
                     <h4>Phone</h4>
                     <span><a href="tel:+250788307386" style="color: inherit; text-decoration: none;">+250 788 307 386</a></span> </div>
                 </div>
                 <div class="info-card">
                   <div class="contact-icon"><i class="fas fa-envelope"></i></div>
                   <div class="info">
                     <h4>Email</h4>
                     <span><a href="mailto:<EMAIL>" style="color: inherit; text-decoration: none;"><EMAIL></a></span>
                   </div>
                 </div>
              </div>

              <div class="contact-form-container" data-aos="fade-left" data-aos-duration="1000">
                <form id="contactForm" class="contact-form" action="https://formspree.io/f/xvgakwnq" method="POST"> <h3>Send Us a Message</h3>
                  <div class="input-box">
                    <input type="text" name="name" id="name" placeholder="Your Name" required>
                  </div>
                  <div class="input-box">
                    <input type="email" name="email" id="email" placeholder="Your Email" required>
                  </div>
                   <div class="input-box">
                    <input type="text" name="subject" id="subject" placeholder="Subject">
                  </div>
                  <div class="input-box">
                    <textarea name="message" id="message" rows="6" placeholder="Your Message" required></textarea>
                  </div>

                  <div class="input-box">
                    <button type="submit"
                      class="g-recaptcha btn send-btn"
                      data-sitekey="6LcY-iwrAAAAAPzOFEUS-oL8sUE_MrwlmMFX77oi"
                      data-callback="onSubmit"
                      data-action="submit">Send Message</button>
                  </div>
                  <div id="formStatus" class="form-status"></div>
                </form>
              </div>
            </div>

            <!-- Google Map -->
            <div class="map-container" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="200">
              <h3 class="content-title">Our Location</h3>
              <div class="google-map">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3987.4458825670544!2d30.068861515209937!3d-1.9239279984612582!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMcKwNTUnMjUuOCJTIDMwwrAwNCcxNS44IkU!5e0!3m2!1sen!2srw!4v1652345678901!5m2!1sen!2srw"
                  width="100%"
                  height="450"
                  style="border:0;"
                  allowfullscreen=""
                  loading="lazy"
                  referrerpolicy="no-referrer-when-downgrade">
                </iframe>
              </div>
            </div>
        </div>
      </section>

      <footer class="footer">
        <div class="section-container footer-content">
             <span class="footer-title">Malva Trading LTD</span>
             <div class="footer-links">
                 <a href="#about">About</a> |
                 <a href="#services">Services</a> |
                 <a href="#gallery">Gallery</a> |
                 <a href="#contact">Contact</a>
                 </div>
             <div class="footer-social">
                <a href="https://wa.me/250788307386" target="_blank" aria-label="Contact Malva Trading on WhatsApp"><i class="fab fa-whatsapp"></i></a>
                <a href="https://x.com/ngogapaul51" target="_blank" aria-label="Malva Trading on Twitter"><i class="fab fa-x-twitter"></i></a>
                <a href="https://www.instagram.com/malvatrading?igsh=MXNjNnV6eDh1eXVxYg==" target="_blank" aria-label="Malva Trading on Instagram"><i class="fab fa-instagram"></i></a>
                <a href="https://www.facebook.com/share/1AT84mVnRx/" target="_blank" aria-label="Malva Trading on Facebook"><i class="fab fa-facebook"></i></a>
                <a href="https://www.tiktok.com/@malva.trading.ltd?_t=ZM-8wIBIURlruf&_r=1" target="_blank" aria-label="Malva Trading on TikTok"><i class="fab fa-tiktok"></i></a>
                </div>
             <p>&copy; <span id="current-year"></span> Malva Trading LTD. All Rights Reserved.</p>
             </div>
      </footer>

      <button class="scrollToTop-btn" aria-label="Scroll to top">
        <i class="fas fa-arrow-up"></i>
      </button>

    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script src="https://www.google.com/recaptcha/enterprise.js?render=6LcY-iwrAAAAAPzOFEUS-oL8sUE_MrwlmMFX77oi"></script>
    <script>
      AOS.init({
          duration: 800, // Default duration for animations
          once: true, // Animate elements only once
          offset: 50 // Offset (in px) from the original trigger point
      });

      // --- Navigation ---
      const menuBtn = document.querySelector(".menu-btn");
      const navigation = document.querySelector(".navigation");
      const navigationItems = document.querySelectorAll(".navigation a"); // Corrected selector
      const header = document.querySelector("header");
      const scrollToTopBtn = document.querySelector(".scrollToTop-btn");

      // Toggle mobile menu
      menuBtn.addEventListener("click", () => {
          menuBtn.classList.toggle("active");
          navigation.classList.toggle("active");
          // Prevent scrolling when menu is open
          document.body.classList.toggle("menu-open");
      });

      // Close mobile menu when a link is clicked
      navigationItems.forEach((item) => {
          item.addEventListener("click", () => {
              menuBtn.classList.remove("active");
              navigation.classList.remove("active");
              document.body.classList.remove("menu-open");
          });
      });

      // --- Sticky Header & Scroll Top Button ---
      window.addEventListener("scroll", function() {
          // Sticky Header
          header.classList.toggle("sticky", window.scrollY > 50); // Use a smaller threshold

          // Scroll to Top Button Visibility
          if (scrollToTopBtn) { // Check if button exists
            scrollToTopBtn.classList.toggle("active", window.scrollY > 500);
          }
      });

      // Scroll to Top Functionality
      if (scrollToTopBtn) { // Check if button exists
          scrollToTopBtn.addEventListener("click", () => {
              window.scrollTo({
                  top: 0,
                  behavior: 'smooth' // Smooth scroll
              });
          });
      }

      // --- Contact Form Submission with Formspree and reCAPTCHA Enterprise ---
      const contactForm = document.getElementById('contactForm');
      const formStatus = document.getElementById('formStatus');

      // Function for reCAPTCHA callback - this is called when reCAPTCHA verification succeeds
      function onSubmit(token) {
          // Show loading message
          formStatus.innerHTML = '<p class="sending">Sending message...</p>';
          formStatus.style.display = 'block';

          // Get form data
          const formData = new FormData(contactForm);

          // Convert to JSON object for fetch API
          const jsonData = {
              name: document.getElementById('name').value,
              email: document.getElementById('email').value,
              subject: document.getElementById('subject').value,
              message: document.getElementById('message').value,
              'g-recaptcha-response': token // Add the reCAPTCHA token
          };

          // Send data to Formspree
          fetch('https://formspree.io/f/xvgakwnq', {
              method: 'POST',
              headers: {
                  'Content-Type': 'application/json'
              },
              body: JSON.stringify(jsonData)
          })
          .then(response => {
              if (response.ok) {
                  // Success message
                  formStatus.innerHTML = '<p class="success">Message sent successfully! We will get back to you soon.</p>';
                  contactForm.reset(); // Clear the form
              } else {
                  // Error message
                  formStatus.innerHTML = '<p class="error">Failed to send message. Please try again later.</p>';
              }

              // Hide status message after 5 seconds
              setTimeout(() => {
                  formStatus.style.display = 'none';
              }, 5000);
          })
          .catch(error => {
              console.error('Error:', error);
              formStatus.innerHTML = '<p class="error">An error occurred. Please try again later.</p>';

              // Hide status message after 5 seconds
              setTimeout(() => {
                  formStatus.style.display = 'none';
              }, 5000);
          });
      }

      // Prevent default form submission
      if (contactForm) {
          contactForm.addEventListener('submit', function(e) {
              e.preventDefault();
              // The actual submission is handled by the reCAPTCHA callback (onSubmit function)
          });
      }

      // --- Footer Year ---
      document.getElementById('current-year').textContent = new Date().getFullYear();

      // --- Gallery Filtering ---
      const filterButtons = document.querySelectorAll('.filter-btn');
      const galleryItems = document.querySelectorAll('.gallery-item');

      // Filter gallery items
      filterButtons.forEach(button => {
          button.addEventListener('click', () => {
              // Remove active class from all buttons
              filterButtons.forEach(btn => btn.classList.remove('active'));

              // Add active class to clicked button
              button.classList.add('active');

              const filterValue = button.getAttribute('data-filter');

              galleryItems.forEach(item => {
                  if (filterValue === 'all' || item.classList.contains(filterValue)) {
                      item.classList.remove('hide');
                  } else {
                      item.classList.add('hide');
                  }
              });
          });
      });

      // Simple lightbox functionality
      galleryItems.forEach(item => {
          item.addEventListener('click', () => {
              const imgSrc = item.querySelector('img').getAttribute('src');
              const title = item.querySelector('img').getAttribute('alt');

              // Create lightbox elements
              const lightbox = document.createElement('div');
              lightbox.className = 'lightbox';

              lightbox.innerHTML = `
                  <div class="lightbox-content">
                      <span class="close-lightbox">&times;</span>
                      <img src="${imgSrc}" alt="${title}">
                      <div class="lightbox-caption">${title}</div>
                  </div>
              `;

              // Add lightbox to body
              document.body.appendChild(lightbox);

              // Prevent scrolling when lightbox is open
              document.body.style.overflow = 'hidden';

              // Close lightbox when clicking on it
              lightbox.addEventListener('click', (e) => {
                  if (e.target.className === 'lightbox' || e.target.className === 'close-lightbox') {
                      document.body.removeChild(lightbox);
                      document.body.style.overflow = 'auto';
                  }
              });
          });
      });

    </script>
    <!--Start of Tawk.to Script-->
<script type="text/javascript">
    var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
    (function(){
    var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
    s1.async=true;
    s1.src='https://embed.tawk.to/681a4970c29c9a1910bdd568/1iqjato1m';
    s1.charset='UTF-8';
    s1.setAttribute('crossorigin','*');
    s0.parentNode.insertBefore(s1,s0);
    })();
    </script>
    <!--End of Tawk.to Script-->
</body>
</html>