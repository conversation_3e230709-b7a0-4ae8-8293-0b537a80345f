@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* --- CSS Variables --- */
:root {
    --primary-color: #213afc; /* #3a6cf4; */
    --secondary-color: #1f5ae3;
    --dark-bg: #04002d; /* #000b28; */
    --light-bg: #ffffff;
    --dark-text: #111111;
    --light-text: #ffffff;
    --grey-text: #666666;
    --card-shadow: rgba(0, 0, 0, 0.1);
    --section-padding: 5rem 0; /* Vertical padding */
    --container-padding: 0 1rem; /* Horizontal padding for container */
    --container-max-width: 1200px;
    --base-font-size: 16px;
    --font-family: 'Poppins', sans-serif;
    --border-radius: 8px;
    --transition-speed: 0.3s;
}

/* --- Global Reset & Base Styles --- */
* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: var(--base-font-size);
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--dark-text);
    background-color: var(--light-bg);
    overflow-x: hidden; /* Prevent horizontal scroll */
    width: 100%;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: color var(--transition-speed) ease;
}

a:hover {
    color: var(--secondary-color);
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

ul {
    list-style: none; /* Remove default list styles */
    padding-left: 0; /* Remove default padding */
}

/* --- Utility Classes --- */
.section-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: var(--container-padding);
}

.dark-section {
    background-color: var(--dark-bg);
    color: var(--light-text);
}

.dark-section .section-title {
    color: var(--light-text);
}
.dark-section .section-title::before,
.dark-section .section-title::after {
    background-color: var(--primary-color);
}
.dark-section .content-title {
     color: var(--light-text);
}
.dark-section .paragraph-text {
     color: rgba(255, 255, 255, 0.85);
}
.dark-section a:not(.btn) {
     color: #a7c0ff; /* Lighter link color for dark bg */
}
.dark-section a:not(.btn):hover {
     color: var(--light-text);
}


.btn {
    display: inline-block;
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: 0.8rem 1.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-radius: var(--border-radius);
    border: none;
    cursor: pointer;
    transition: background-color var(--transition-speed) ease, transform var(--transition-speed) ease;
    text-align: center;
}

.btn:hover {
    background-color: var(--secondary-color);
    color: var(--light-text); /* Ensure text color remains on hover */
    transform: translateY(-2px);
}

/* --- Header --- */
header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 999;
    background-color: rgba(4, 0, 45, 0.3); /* Semi-transparent initial */
    padding: 1rem 0;
    transition: background-color var(--transition-speed) ease, padding var(--transition-speed) ease;
    backdrop-filter: blur(5px); /* Optional: Add blur effect */
    -webkit-backdrop-filter: blur(5px); /* Safari */
}

header.sticky {
    background-color: var(--dark-bg);
    padding: 0.75rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: var(--container-padding);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

header .brand {
    color: var(--light-text);
    font-size: 1.6rem;
    font-weight: 700;
    text-transform: uppercase;
}

header .navigation a {
    color: var(--light-text);
    font-size: 1rem;
    font-weight: 500;
    margin-left: 2rem;
    transition: color var(--transition-speed) ease;
}

header .navigation a:hover {
    color: var(--primary-color);
}

/* Mobile Menu Button */
.menu-btn {
    display: none; /* Hidden by default */
    width: 30px;
    height: 25px;
    cursor: pointer;
    position: relative;
    z-index: 1000; /* Ensure it's above navigation */
}

.menu-btn::before,
.menu-btn::after,
.menu-btn span {
    content: '';
    position: absolute;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--light-text);
    transition: transform var(--transition-speed) ease, top var(--transition-speed) ease, opacity var(--transition-speed) ease;
}

.menu-btn::before {
    top: 5px;
}

.menu-btn::after {
    top: 17px;
}

/* Middle line for hamburger icon */
.menu-btn span {
    top: 11px; /* Middle position */
}

/* Menu button animation when active */
.menu-btn.active::before {
    top: 11px;
    transform: rotate(45deg);
}

.menu-btn.active::after {
    top: 11px;
    transform: rotate(-45deg);
}

/* Hide middle line when active */
.menu-btn.active span {
    opacity: 0;
}


/* --- Main Hero Section --- */
section {
    padding: var(--section-padding);
}

.main {
    position: relative;
    min-height: 100vh; /* Use min-height instead of height */
    display: flex;
    align-items: center;
    background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7)), url('Image/imagee2.jpg'); /* African construction worker image */
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed; /* Keep parallax if desired, test performance */
    padding-top: 100px; /* Adjust padding top to account for fixed header height */
    padding-bottom: 100px;
}

.main .hero-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    position: relative;
    z-index: 2;
}

.main .main-content {
    color: var(--light-text);
    width: 50%;
    padding-right: 2rem;
}

.main .hero-image {
    width: 45%;
    position: relative;
}

.main .hero-image img {
    width: 100%;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    opacity: 0;
    transform: translateX(50px);
    animation: slideIn 1s ease-out 0.8s forwards;
}

@keyframes slideIn {
    from { opacity: 0; transform: translateX(50px); }
    to { opacity: 1; transform: translateX(0); }
}

.main h1 { /* Use H1 for main page title */
    font-size: clamp(2.5rem, 6vw, 4rem); /* Responsive font size */
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    position: relative;
    display: inline-block;
}

.main h1::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 80px;
    height: 4px;
    background-color: var(--primary-color);
    animation: expandWidth 1.5s ease-out forwards;
}

@keyframes expandWidth {
    from { width: 0; }
    to { width: 80px; }
}

.main h1 span { /* If using the CEO title structure */
    display: block; /* Make span take full width for line break */
    font-size: 0.8em; /* Slightly smaller than main title */
}

.main .subtitle {
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    font-weight: 300;
    margin-bottom: 2rem;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.6);
    opacity: 0;
    animation: fadeIn 1s ease-out 0.5s forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive hero section */
@media (max-width: 992px) {
    .main .hero-container {
        flex-direction: column;
    }

    .main .main-content,
    .main .hero-image {
        width: 100%;
        padding-right: 0;
    }

    .main .hero-image {
        margin-top: 3rem;
    }

    .main h1 {
        font-size: 2.2rem;
    }

    .main .subtitle {
        font-size: 1.1rem;
        max-width: 100%;
    }
}

@media (max-width: 576px) {
    .main {
        padding-top: 80px;
        padding-bottom: 60px;
    }

    .main h1 {
        font-size: 1.8rem;
    }

    .main .subtitle {
        font-size: 1rem;
    }

    .main .hero-image {
        margin-top: 2rem;
    }

    .media-icons {
        margin-top: 2rem;
    }

    .media-icons a {
        font-size: 1.5rem;
        margin-right: 1rem;
    }
}

/* Animated Text (Optional) */
.animated-text {
    position: relative;
    height: 70px; /* Adjust as needed */
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.animated-text h3 {
    color: var(--primary-color);
    font-size: clamp(2rem, 5vw, 3rem); /* Responsive */
    font-weight: 700;
    line-height: 70px; /* Match height */
    letter-spacing: 1px;
    position: absolute;
    width: 100%;
    margin: 0;
}

.animated-text h3:nth-child(1) { animation: text-move 6s infinite ease-out; }
/* Keep @keyframes text-move as is */
@keyframes text-move {
    0%,15%{ margin-top: 0px; opacity: 1; }
    20% { opacity: 0; } /* Fade out */
    25%,40%{ margin-top: -70px; opacity: 1; } /* Next item fades in */
    45% { opacity: 0; }
    50%,65%{ margin-top: -140px; opacity: 1; }
    70% { opacity: 0; }
    75%,90%{ margin-top: -70px; opacity: 1; } /* Cycle back (optional) */
    95% { opacity: 0; }
    100%{ margin-top: 0px; opacity: 1; }
}


.hero-btn {
    margin-top: 1rem;
}

.media-icons {
    margin-top: 3rem;
}

.media-icons a {
    color: var(--light-text);
    font-size: 1.8rem;
    margin-right: 1.5rem;
    transition: color var(--transition-speed) ease, transform var(--transition-speed) ease;
}

.media-icons a:hover {
    color: var(--primary-color);
    transform: scale(1.1);
}


/* --- Section Title Styling --- */
.title {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    position: relative;
    color: var(--dark-text); /* Default for light sections */
    font-size: clamp(1.8rem, 4vw, 2.5rem);
    font-weight: 700;
    margin-bottom: 1rem; /* Space below title */
    padding-bottom: 0.8rem; /* Space for the line */
    display: inline-block; /* Allow centering and line positioning */
}

.section-title::after { /* Simplified underline */
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px; /* Adjust width */
    height: 4px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--grey-text);
    max-width: 700px;
    margin: 0 auto; /* Center subtitle */
}

.dark-section .section-subtitle {
    color: rgba(255, 255, 255, 0.7);
}

/* --- Two Column Layout (About, Business) --- */
.two-column-layout {
    display: flex;
    flex-wrap: wrap; /* Allow wrapping on smaller screens */
    gap: 3rem; /* Space between columns */
    align-items: center; /* Vertically align items */
}

.two-column-layout .column {
    flex: 1; /* Each column takes equal space */
    min-width: 300px; /* Minimum width before wrapping */
}

.two-column-layout .img-card {
    position: relative;
    width: 100%;
    border-radius: var(--border-radius);
    overflow: hidden; /* Ensure image stays within bounds */
    box-shadow: 0 10px 20px var(--card-shadow);
}

.two-column-layout .img-card img {
    width: 100%;
    height: 100%;
    object-fit: cover; /* Crop image nicely */
    min-height: 350px; /* Ensure image card has some height */
}

.content-title {
    font-size: clamp(1.5rem, 3vw, 2rem);
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--dark-text); /* Default */
}

.paragraph-text {
    font-size: 1rem;
    margin-bottom: 1.5rem;
    line-height: 1.7;
    color: var(--grey-text); /* Default */
}

/* --- Card Grid Layout (Services, Projects) --- */
.card-grid-3 {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); /* Responsive grid */
    gap: 2rem; /* Space between cards */
    margin-top: 2rem;
}

.card {
    background-color: var(--light-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px var(--card-shadow);
    transition: transform var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
    text-align: center;
    display: flex; /* Use flex for better control */
    flex-direction: column; /* Stack icon and info */
    align-items: center; /* Center items horizontally */
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Service Cards */
.services .card .service-icon {
    font-size: 4rem; /* Adjusted size */
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    transition: transform var(--transition-speed) ease;
    width: 80px; /* Give icon fixed size */
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #eef2ff; /* Light background for icon */
    border-radius: 50%;
}

.services .card:hover .service-icon {
    transform: scale(1.1);
}

.services .card .info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.8rem;
    color: var(--dark-text);
}

.services .card .info p,
.services .card .info ul {
    font-size: 0.95rem;
    color: var(--grey-text);
    text-align: left; /* Align list items left for readability */
}

.services .card .info ul {
    margin-top: 0.8rem;
    padding-left: 1rem; /* Indent list */
}

.services .card .info ul li {
    margin-bottom: 0.4rem;
    position: relative;
    padding-left: 1.2rem; /* Space for pseudo-element */
}
.services .card .info ul li::before {
    content: '\f00c'; /* Font Awesome check icon */
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: var(--primary-color);
    position: absolute;
    left: 0;
    top: 3px;
    font-size: 0.8rem;
}


/* Gallery Section */
.gallery {
    background-color: var(--light-bg);
    position: relative;
}

/* Gallery Filter Buttons */
.gallery-filter {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.8rem;
    margin: 2rem 0;
}

.filter-btn {
    padding: 0.6rem 1.2rem;
    background-color: #f5f5f5;
    border: none;
    border-radius: 30px;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--dark-text);
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover, .filter-btn.active {
    background-color: var(--primary-color);
    color: white;
}

.gallery-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.gallery-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    height: 300px;
    transition: transform 0.4s ease, box-shadow 0.4s ease;
}

.gallery-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.gallery-img {
    width: 100%;
    height: 100%;
    position: relative;
}

.gallery-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.gallery-item:hover .gallery-img img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 1rem;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    color: white;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-overlay h3 {
    font-size: 1.1rem;
    margin: 0;
    font-weight: 500;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

/* Hide items that don't match the filter */
.gallery-item.hide {
    display: none;
}

/* Lightbox effect for gallery images */
.gallery-img {
    cursor: pointer;
}

/* Lightbox styles */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.lightbox-content img {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
    border-radius: 4px;
}

.close-lightbox {
    position: absolute;
    top: -40px;
    right: 0;
    color: white;
    font-size: 30px;
    cursor: pointer;
}

.lightbox-caption {
    color: white;
    text-align: center;
    padding: 10px 0;
    font-size: 1.1rem;
}

@media (max-width: 768px) {
    .gallery-filter {
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .gallery-filter {
        flex-direction: row;
        justify-content: center;
        gap: 0.4rem;
        margin: 1.5rem 0;
    }

    .filter-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
        margin-bottom: 0.5rem;
    }
}


/* --- Contact Section --- */
.contact-content {
    display: flex;
    flex-wrap: wrap;
    gap: 3rem;
    margin-top: 2rem;
}

.contact-info, .contact-form-container {
    flex: 1;
    min-width: 300px;
}

.contact-info .content-title {
    margin-bottom: 2rem;
}

.contact-info .info-card {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    background-color: rgba(255, 255, 255, 0.05); /* Subtle background on dark */
    padding: 1rem;
    border-radius: var(--border-radius);
    transition: background-color var(--transition-speed) ease;
}
.contact-info .info-card:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.contact-info .contact-icon {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-right: 1rem;
    width: 40px; /* Fixed width */
    text-align: center;
}

.contact-info .info h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.2rem;
    color: var(--light-text);
}

.contact-info .info span {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
}

.contact-form {
    background: var(--light-bg); /* Form on dark bg needs light background */
    padding: 2.5rem;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px var(--card-shadow);
}

.contact-form h3 {
    color: var(--dark-text);
    font-size: 1.6rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: 2rem;
}

.contact-form .input-box {
    margin-bottom: 1.5rem;
}

.contact-form .input-box input,
.contact-form .input-box textarea {
    width: 100%;
    padding: 0.8rem 1rem;
    font-size: 1rem;
    font-family: var(--font-family);
    border: 1px solid #ccc;
    border-radius: var(--border-radius);
    outline: none;
    transition: border-color var(--transition-speed) ease;
    color: var(--dark-text); /* Ensure text input is visible */
}
.contact-form .input-box input:focus,
.contact-form .input-box textarea:focus {
    border-color: var(--primary-color);
}

.contact-form .input-box textarea {
    resize: vertical; /* Allow vertical resize only */
    min-height: 120px;
}

.contact-form .send-btn {
    width: 100%;
    padding: 1rem; /* Larger button */
    font-size: 1.1rem;
}

/* Form status messages */
.form-status {
    margin-top: 1rem;
    padding: 0.75rem;
    border-radius: 5px;
    display: none;
    text-align: center;
}

.form-status .sending {
    color: var(--primary-color);
    font-weight: 500;
}

.form-status .success {
    color: #28a745;
    font-weight: 500;
}

.form-status .error {
    color: #dc3545;
    font-weight: 500;
}

/* reCAPTCHA Styling */
.recaptcha-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 1.5rem;
}

.recaptcha-note {
    font-size: 0.85rem;
    color: #666;
    margin-top: 0.5rem;
}

/* Center reCAPTCHA on mobile */
@media (max-width: 300px) {
    .g-recaptcha {
        transform: scale(0.85);
        transform-origin: center;
    }
}


/* --- Footer --- */
.footer {
    background: var(--dark-bg);
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
    padding: 2.5rem 0;
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.footer .footer-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--light-text);
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    margin: 0 0.5rem;
    font-size: 0.95rem;
}
.footer-links a:hover {
     color: var(--light-text);
}

.footer-social a {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.5rem;
    margin: 0 0.7rem;
    transition: color var(--transition-speed) ease, transform var(--transition-speed) ease;
}
.footer-social a:hover {
     color: var(--primary-color);
     transform: scale(1.1);
}

.footer p {
    font-size: 0.9rem;
    margin-top: 1rem;
}


/* --- Scroll To Top Button --- */
.scrollToTop-btn {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(20px); /* Center horizontally and start off-screen */
    background: var(--primary-color);
    color: var(--light-text);
    width: 45px;
    height: 45px;
    border-radius: var(--border-radius);
    font-size: 1.5rem; /* Adjusted size */
    line-height: 45px; /* Center icon */
    text-align: center;
    cursor: pointer;
    opacity: 0;
    visibility: hidden; /* Use visibility for better accessibility */
    transition: opacity var(--transition-speed) ease, visibility var(--transition-speed) ease, transform var(--transition-speed) ease;
    border: none;
    z-index: 990;
}

.scrollToTop-btn.active {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0); /* Keep centered when active */
}
.scrollToTop-btn:hover {
    background-color: var(--secondary-color);
}

/* --- Fixed Widget Positioning --- */
/* Position reCAPTCHA widget on the left */
.grecaptcha-badge {
    left: 20px !important;
    right: auto !important;
    bottom: 20px !important;
    z-index: 985 !important;
}

/* Position Tawk.to chat widget on the right */
#tawkchat-minified-container,
#tawkchat-container {
    right: 20px !important;
    bottom: 20px !important;
    z-index: 985 !important;
}

/* --- Responsive Design --- */
@media (max-width: 1040px) {
    :root {
        --section-padding: 4rem 0;
    }

    header .navigation {
        display: none; /* Hide desktop nav */
        position: fixed;
        top: 0;
        right: -100%; /* Start off-screen */
        width: 80%; /* Use percentage for better responsiveness */
        max-width: 300px; /* Maximum width */
        height: 100vh;
        background-color: rgba(4, 0, 45, 0.95); /* Semi-transparent background */
        flex-direction: column;
        justify-content: center;
        align-items: center;
        transition: right var(--transition-speed) ease;
        z-index: 998; /* Below menu button */
        box-shadow: -5px 0 15px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px); /* Add blur effect */
        -webkit-backdrop-filter: blur(10px); /* Safari support */
    }

    header .navigation.active {
        display: flex; /* Show mobile nav */
        right: 0;
    }

    header .navigation a {
        color: var(--light-text);
        font-size: 1.2rem;
        margin: 0.8rem 0; /* Reduced vertical spacing */
        padding: 0.5rem 1rem;
        display: block;
        width: 100%;
        text-align: center;
        border-radius: 4px;
        transition: background-color 0.2s ease, color 0.2s ease;
    }

    header .navigation a:hover {
        background-color: var(--primary-color);
        color: white;
    }

    .menu-btn {
        display: block; /* Show menu button */
    }

    /* Reverse layout for two-column sections on mobile if needed */
    .two-column-layout.reverse-layout-mobile {
        flex-direction: column-reverse; /* Image below text */
    }

    .two-column-layout.reverse-layout-mobile .column {
        width: 100%; /* Ensure full width */
    }

    .two-column-layout.reverse-layout-mobile .col-right {
        margin-bottom: 2rem; /* Add space below text when reversed */
    }

    /* Fix for any potential horizontal overflow */
    body {
        overflow-x: hidden;
    }

    /* Prevent scrolling when mobile menu is open */
    body.menu-open {
        overflow: hidden;
    }

    .section-container {
        width: 100%;
        padding: 0 15px; /* Consistent padding */
    }
}

@media (max-width: 768px) {
    :root {
        --section-padding: 3rem 0;
    }

    .main h1 {
        font-size: 2.2rem;
    }
    .main .subtitle {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.6rem;
    }
    .section-subtitle {
        font-size: 1rem;
    }

    .content-title {
         font-size: 1.4rem;
    }

    .two-column-layout {
        gap: 2rem;
    }

    .card-grid-3 {
        grid-template-columns: 1fr; /* Stack cards */
        gap: 1.5rem;
    }

    .contact-content {
        gap: 2rem;
    }
    .contact-form {
        padding: 1.5rem;
    }

    .footer-content {
        gap: 0.8rem;
    }

    .footer-social a {
         font-size: 1.3rem;
         margin: 0 0.5rem;
    }

    /* Improve gallery layout on mobile */
    .gallery-container {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    /* Adjust card padding for better mobile display */
    .card {
        padding: 1.5rem;
    }

    /* Adjust widget positioning for mobile */
    .grecaptcha-badge {
        left: 10px !important;
        bottom: 80px !important; /* Move up to avoid overlap with scroll button */
    }

    #tawkchat-minified-container,
    #tawkchat-container {
        right: 10px !important;
        bottom: 80px !important; /* Move up to avoid overlap with scroll button */
    }

    .scrollToTop-btn {
        bottom: 15px;
        width: 40px;
        height: 40px;
        font-size: 1.3rem;
        line-height: 40px;
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    :root {
        --section-padding: 2.5rem 0;
    }

    .header-container {
        padding: 0 10px;
    }

    header .brand {
        font-size: 1.3rem;
    }

    .main h1 {
        font-size: 1.8rem;
    }

    .main .subtitle {
        font-size: 0.9rem;
    }

    .btn {
        padding: 0.7rem 1.5rem;
        font-size: 0.9rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .section-subtitle {
        font-size: 0.9rem;
    }

    .paragraph-text {
        font-size: 0.95rem;
    }

    /* Make gallery items taller on very small screens */
    .gallery-item {
        height: 250px;
    }

    /* Adjust contact form for very small screens */
    .contact-form {
        padding: 1.2rem;
    }

    .contact-form h3 {
        font-size: 1.3rem;
    }

    .contact-form .input-box input,
    .contact-form .input-box textarea {
        padding: 0.7rem;
        font-size: 0.95rem;
    }

    /* Footer adjustments */
    .footer {
        padding: 2rem 0;
    }

    .footer .footer-title {
        font-size: 1.3rem;
    }

    .footer-links a {
        font-size: 0.85rem;
    }

    /* Service cards adjustments */
    .services .card .service-icon {
        font-size: 3rem;
        width: 60px;
        height: 60px;
    }

    .services .card .info h3 {
        font-size: 1.2rem;
    }

    .services .card .info p,
    .services .card .info ul {
        font-size: 0.9rem;
    }

    /* Further adjust widget positioning for small screens */
    .grecaptcha-badge {
        left: 5px !important;
        bottom: 90px !important;
        transform: scale(0.9) !important;
    }

    #tawkchat-minified-container,
    #tawkchat-container {
        right: 5px !important;
        bottom: 90px !important;
    }

    .scrollToTop-btn {
        bottom: 10px;
        width: 38px;
        height: 38px;
        font-size: 1.2rem;
        line-height: 38px;
    }
}

/* Very small devices */
@media (max-width: 360px) {
    header .brand {
        font-size: 1.1rem;
    }

    .main h1 {
        font-size: 1.6rem;
    }

    .main .subtitle {
        font-size: 0.85rem;
    }

    .btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.85rem;
    }

    .section-title {
        font-size: 1.4rem;
    }

    /* Ensure no horizontal scrolling */
    .section-container {
        padding: 0 10px;
    }

    /* Adjust gallery for very small screens */
    .gallery-container {
        grid-template-columns: 1fr;
    }

    /* Ensure images don't cause overflow */
    img {
        max-width: 100%;
        height: auto;
    }
}

/* --- Google Map Styling --- */
.map-container {
    margin-top: 3rem;
    width: 100%;
}

.google-map {
    width: 100%;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 5px 15px var(--card-shadow);
    margin-top: 1rem;
}

.google-map iframe {
    display: block;
    border-radius: var(--border-radius);
}

@media (max-width: 768px) {
    .google-map iframe {
        height: 350px;
    }
}